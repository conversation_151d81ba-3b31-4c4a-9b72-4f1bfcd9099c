// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		A0001B6F270B879400F40C2F /* RemoteServerSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0001B6E270B879400F40C2F /* RemoteServerSettingsView.swift */; };
		A01056A524F48D5E00DA056D /* TorrentHandlerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A01056A424F48D5E00DA056D /* TorrentHandlerView.swift */; };
		A01056A824F4901700DA056D /* LocalTorrent+Initializers.swift in Sources */ = {isa = PBXBuildFile; fileRef = A01056A724F4901700DA056D /* LocalTorrent+Initializers.swift */; };
		A01056A924F4901700DA056D /* LocalTorrent+Initializers.swift in Sources */ = {isa = PBXBuildFile; fileRef = A01056A724F4901700DA056D /* LocalTorrent+Initializers.swift */; };
		A01056AB24F491B600DA056D /* String+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A01056AA24F491B600DA056D /* String+Extensions.swift */; };
		A01056AC24F491B600DA056D /* String+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A01056AA24F491B600DA056D /* String+Extensions.swift */; };
		A011C7E524F20C8D009D72C6 /* RemoteTorrent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7E424F20C8D009D72C6 /* RemoteTorrent.swift */; };
		A011C7E624F20C8D009D72C6 /* RemoteTorrent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7E424F20C8D009D72C6 /* RemoteTorrent.swift */; };
		A011C7EF24F21078009D72C6 /* ServerConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7EE24F21078009D72C6 /* ServerConnection.swift */; };
		A011C7F024F21078009D72C6 /* ServerConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7EE24F21078009D72C6 /* ServerConnection.swift */; };
		A011C7F224F21088009D72C6 /* TransmissionConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7F124F21088009D72C6 /* TransmissionConnection.swift */; };
		A011C7F324F21088009D72C6 /* TransmissionConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7F124F21088009D72C6 /* TransmissionConnection.swift */; };
		A011C7F524F211C6009D72C6 /* LocalTorrent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7F424F211C6009D72C6 /* LocalTorrent.swift */; };
		A011C7F624F211C6009D72C6 /* LocalTorrent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7F424F211C6009D72C6 /* LocalTorrent.swift */; };
		A012561325046F0D00C7D51B /* SeedTruckApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A012561225046F0D00C7D51B /* SeedTruckApp.swift */; };
		A022E6522C8FBC7200BAEED3 /* SwiftyBencode in Frameworks */ = {isa = PBXBuildFile; productRef = A022E6512C8FBC7200BAEED3 /* SwiftyBencode */; };
		A022E6542C8FBC8400BAEED3 /* SwiftyBencode in Frameworks */ = {isa = PBXBuildFile; productRef = A022E6532C8FBC8400BAEED3 /* SwiftyBencode */; };
		A022E6562C8FBC9000BAEED3 /* SwiftyBencode in Frameworks */ = {isa = PBXBuildFile; productRef = A022E6552C8FBC9000BAEED3 /* SwiftyBencode */; };
		A022E6582C8FBCCE00BAEED3 /* SwiftyBencode in Frameworks */ = {isa = PBXBuildFile; productRef = A022E6572C8FBCCE00BAEED3 /* SwiftyBencode */; };
		A023BAE424F294E400B82FB3 /* TorrentDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A023BAE324F294E400B82FB3 /* TorrentDetailsView.swift */; };
		A023BAE524F294E400B82FB3 /* TorrentDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A023BAE324F294E400B82FB3 /* TorrentDetailsView.swift */; };
		A023BAE724F294EF00B82FB3 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A023BAE624F294EF00B82FB3 /* SettingsView.swift */; };
		A024CF9D258583500068844E /* DataTransferManageable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E742792515501F0029FFD6 /* DataTransferManageable.swift */; };
		A02C584F24F325A400C82D68 /* DataModel.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = A02C584D24F325A400C82D68 /* DataModel.xcdatamodeld */; };
		A02C585024F325A400C82D68 /* DataModel.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = A02C584D24F325A400C82D68 /* DataModel.xcdatamodeld */; };
		A02C585224F326EA00C82D68 /* RemoteTorrent+Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585124F326EA00C82D68 /* RemoteTorrent+Transmission.swift */; };
		A02C585324F326EA00C82D68 /* RemoteTorrent+Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585124F326EA00C82D68 /* RemoteTorrent+Transmission.swift */; };
		A02C585624F3400200C82D68 /* Server.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585524F3400200C82D68 /* Server.swift */; };
		A02C585724F3400200C82D68 /* Server.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585524F3400200C82D68 /* Server.swift */; };
		A02C585924F3446100C82D68 /* NewServerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585824F3446100C82D68 /* NewServerView.swift */; };
		A045C8BE24F2006500B3B1CD /* Tests_iOS.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8BD24F2006500B3B1CD /* Tests_iOS.swift */; };
		A045C8C924F2006500B3B1CD /* Tests_macOS.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8C824F2006500B3B1CD /* Tests_macOS.swift */; };
		A045C8CB24F2006500B3B1CD /* SeedTruckApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8A224F2006400B3B1CD /* SeedTruckApp.swift */; };
		A045C8CF24F2006500B3B1CD /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A045C8A424F2006500B3B1CD /* Assets.xcassets */; };
		A045C8E024F2011E00B3B1CD /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8DF24F2011E00B3B1CD /* MainView.swift */; };
		A045C8E324F2013100B3B1CD /* TorrentsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E224F2013100B3B1CD /* TorrentsView.swift */; };
		A045C8E624F2021100B3B1CD /* TorrentItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E524F2021100B3B1CD /* TorrentItemView.swift */; };
		A045C8E724F2021100B3B1CD /* TorrentItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E524F2021100B3B1CD /* TorrentItemView.swift */; };
		A045C8E924F205B100B3B1CD /* ProgressBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E824F205B100B3B1CD /* ProgressBarView.swift */; };
		A045C8EA24F205B100B3B1CD /* ProgressBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E824F205B100B3B1CD /* ProgressBarView.swift */; };
		A051D54F2E563EF800A94EDA /* LabelPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A051D54C2E563EF800A94EDA /* LabelPickerView.swift */; };
		A051D5502E563EF800A94EDA /* LabelPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A051D54C2E563EF800A94EDA /* LabelPickerView.swift */; };
		A0589FED251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0589FEC251799F30022D839 /* BinaryInteger+Extensions.swift */; };
		A0589FEE251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0589FEC251799F30022D839 /* BinaryInteger+Extensions.swift */; };
		A0589FEF251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0589FEC251799F30022D839 /* BinaryInteger+Extensions.swift */; };
		A0589FF0251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0589FEC251799F30022D839 /* BinaryInteger+Extensions.swift */; };
		A05FE16324F43A5A00B57E45 /* NoServersConfiguredView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16224F43A5A00B57E45 /* NoServersConfiguredView.swift */; };
		A05FE16424F43A5A00B57E45 /* NoServersConfiguredView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16224F43A5A00B57E45 /* NoServersConfiguredView.swift */; };
		A05FE16624F43A7300B57E45 /* ErrorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16524F43A7300B57E45 /* ErrorView.swift */; };
		A05FE16724F43A7300B57E45 /* ErrorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16524F43A7300B57E45 /* ErrorView.swift */; };
		A05FE16A24F45B2700B57E45 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16924F45B2700B57E45 /* LoadingView.swift */; };
		A05FE16B24F45B2700B57E45 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16924F45B2700B57E45 /* LoadingView.swift */; };
		A06DEFA424FC8219004B2DC9 /* NoServersConfiguredView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A06DEFA324FC8219004B2DC9 /* NoServersConfiguredView.swift */; };
		A06DEFA824FD7EF5004B2DC9 /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A06DEFA724FD7EF5004B2DC9 /* MainView.swift */; };
		A071CAC924F3113800B6AC0C /* ByteCountFormatter+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CAC824F3113800B6AC0C /* ByteCountFormatter+Extensions.swift */; };
		A071CACA24F3113800B6AC0C /* ByteCountFormatter+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CAC824F3113800B6AC0C /* ByteCountFormatter+Extensions.swift */; };
		A071CC6E24FBC9BF00E46065 /* Style.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CC6D24FBC9BF00E46065 /* Style.swift */; };
		A071CC6F24FBC9BF00E46065 /* Style.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CC6D24FBC9BF00E46065 /* Style.swift */; };
		A071CC7024FBC9BF00E46065 /* Style.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CC6D24FBC9BF00E46065 /* Style.swift */; };
		A071CC7124FBC9BF00E46065 /* Style.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CC6D24FBC9BF00E46065 /* Style.swift */; };
		A073DD2124F4658700BA37C9 /* AddMagnetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A073DD2024F4658700BA37C9 /* AddMagnetView.swift */; };
		A073DD2224F4658700BA37C9 /* AddMagnetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A073DD2024F4658700BA37C9 /* AddMagnetView.swift */; };
		A073DD2424F4768400BA37C9 /* TorrentDetailsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A073DD2324F4768400BA37C9 /* TorrentDetailsPresenter.swift */; };
		A073DD2524F4768400BA37C9 /* TorrentDetailsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A073DD2324F4768400BA37C9 /* TorrentDetailsPresenter.swift */; };
		A07DD0BF2512C96600559D9A /* Connectable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0BE2512C96600559D9A /* Connectable.swift */; };
		A07DD0C02512C96600559D9A /* Connectable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0BE2512C96600559D9A /* Connectable.swift */; };
		A07DD0C12512C96600559D9A /* Connectable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0BE2512C96600559D9A /* Connectable.swift */; };
		A07DD0C22512C96600559D9A /* Connectable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0BE2512C96600559D9A /* Connectable.swift */; };
		A07DD0F42512C9D400559D9A /* TemporaryServer.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0F32512C9D400559D9A /* TemporaryServer.swift */; };
		A07DD0F52512C9D400559D9A /* TemporaryServer.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0F32512C9D400559D9A /* TemporaryServer.swift */; };
		A07DD0F62512C9D400559D9A /* TemporaryServer.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0F32512C9D400559D9A /* TemporaryServer.swift */; };
		A07DD0F72512C9D400559D9A /* TemporaryServer.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD0F32512C9D400559D9A /* TemporaryServer.swift */; };
		A07DD1012512C9E900559D9A /* ConnectionDetails.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD1002512C9E900559D9A /* ConnectionDetails.swift */; };
		A07DD1022512C9E900559D9A /* ConnectionDetails.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD1002512C9E900559D9A /* ConnectionDetails.swift */; };
		A07DD1032512C9E900559D9A /* ConnectionDetails.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD1002512C9E900559D9A /* ConnectionDetails.swift */; };
		A07DD1042512C9E900559D9A /* ConnectionDetails.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07DD1002512C9E900559D9A /* ConnectionDetails.swift */; };
		A07FFC6D24F2F34E002A78B5 /* Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6C24F2F34E002A78B5 /* Transmission.swift */; };
		A07FFC6E24F2F34E002A78B5 /* Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6C24F2F34E002A78B5 /* Transmission.swift */; };
		A07FFC7024F30044002A78B5 /* Transmission+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6F24F30044002A78B5 /* Transmission+Extensions.swift */; };
		A07FFC7124F30044002A78B5 /* Transmission+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6F24F30044002A78B5 /* Transmission+Extensions.swift */; };
		A07FFC7324F306AC002A78B5 /* TransmissionModelTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC7224F306AC002A78B5 /* TransmissionModelTests.swift */; };
		A08863A52585ADD800443ECB /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863A42585ADD800443ECB /* Constants.swift */; };
		A08863AE2585AE1700443ECB /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863A42585ADD800443ECB /* Constants.swift */; };
		A08863B72585AE1900443ECB /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863A42585ADD800443ECB /* Constants.swift */; };
		A08863C02585AE1900443ECB /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863A42585ADD800443ECB /* Constants.swift */; };
		A08863CA2585B65E00443ECB /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863C92585B65E00443ECB /* View+Extensions.swift */; };
		A08863D32585B66900443ECB /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863C92585B65E00443ECB /* View+Extensions.swift */; };
		A08863DC2585B66A00443ECB /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863C92585B65E00443ECB /* View+Extensions.swift */; };
		A08863E52585B66B00443ECB /* View+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08863C92585B65E00443ECB /* View+Extensions.swift */; };
		A08864182585BB5500443ECB /* GeneralSettingsView+AutoUpdateInterval.swift in Sources */ = {isa = PBXBuildFile; fileRef = A08864062585BB4100443ECB /* GeneralSettingsView+AutoUpdateInterval.swift */; };
		A093B94A25142ACB00F499B4 /* Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6C24F2F34E002A78B5 /* Transmission.swift */; };
		A093B95325142B2400F499B4 /* Transmission+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6F24F30044002A78B5 /* Transmission+Extensions.swift */; };
		A096385B24F568CF00F2062F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A096385A24F568CF00F2062F /* Assets.xcassets */; };
		A096386724F568CF00F2062F /* SeedTruckApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096386624F568CF00F2062F /* SeedTruckApp.swift */; };
		A096386924F568CF00F2062F /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096386824F568CF00F2062F /* MainView.swift */; };
		A096387424F568D000F2062F /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A096387324F568D000F2062F /* Preview Assets.xcassets */; };
		A096387924F568D000F2062F /* SeedTruck (watchOS).app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = A096385824F568CF00F2062F /* SeedTruck (watchOS).app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		A096388224F5697600F2062F /* TorrentDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A023BAE324F294E400B82FB3 /* TorrentDetailsView.swift */; };
		A096388324F5697600F2062F /* TorrentListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3824F4358500C76682 /* TorrentListView.swift */; };
		A096388424F5699900F2062F /* RemoteTorrent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7E424F20C8D009D72C6 /* RemoteTorrent.swift */; };
		A096388524F569A900F2062F /* Server.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585524F3400200C82D68 /* Server.swift */; };
		A096388724F569CA00F2062F /* ServerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096388624F569CA00F2062F /* ServerType.swift */; };
		A096388824F569CA00F2062F /* ServerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096388624F569CA00F2062F /* ServerType.swift */; };
		A096388924F569CA00F2062F /* ServerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096388624F569CA00F2062F /* ServerType.swift */; };
		A096388B24F569DE00F2062F /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16924F45B2700B57E45 /* LoadingView.swift */; };
		A096388C24F569DE00F2062F /* ErrorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16524F43A7300B57E45 /* ErrorView.swift */; };
		A096388D24F569E900F2062F /* PreviewMockData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09656AB24F2C8E8009D0FE1 /* PreviewMockData.swift */; };
		A096389024F56A1200F2062F /* LocalTorrent+ComputedProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096388F24F56A1200F2062F /* LocalTorrent+ComputedProperties.swift */; };
		A096389124F56A1200F2062F /* LocalTorrent+ComputedProperties.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096388F24F56A1200F2062F /* LocalTorrent+ComputedProperties.swift */; };
		A096389424F56A2500F2062F /* TorrentDetailsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A073DD2324F4768400BA37C9 /* TorrentDetailsPresenter.swift */; };
		A096389A24F56A5100F2062F /* Server+CoreData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096389924F56A5100F2062F /* Server+CoreData.swift */; };
		A096389B24F56A5100F2062F /* Server+CoreData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096389924F56A5100F2062F /* Server+CoreData.swift */; };
		A096389D24F56A8200F2062F /* Transmission+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6F24F30044002A78B5 /* Transmission+Extensions.swift */; };
		A096389E24F56A8200F2062F /* Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6C24F2F34E002A78B5 /* Transmission.swift */; };
		A096389F24F56A8A00F2062F /* RemoteTorrent+Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585124F326EA00C82D68 /* RemoteTorrent+Transmission.swift */; };
		A09638A024F56A9300F2062F /* ServerConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7EE24F21078009D72C6 /* ServerConnection.swift */; };
		A09638A124F56A9C00F2062F /* TransmissionConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7F124F21088009D72C6 /* TransmissionConnection.swift */; };
		A09638A224F56B9700F2062F /* TorrentItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E524F2021100B3B1CD /* TorrentItemView.swift */; };
		A09638A324F56B9F00F2062F /* ProgressBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E824F205B100B3B1CD /* ProgressBarView.swift */; };
		A09638A424F56BAE00F2062F /* ByteCountFormatter+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CAC824F3113800B6AC0C /* ByteCountFormatter+Extensions.swift */; };
		A09638A624F56BDA00F2062F /* Box.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638A524F56BDA00F2062F /* Box.swift */; };
		A09638A724F56BDA00F2062F /* Box.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638A524F56BDA00F2062F /* Box.swift */; };
		A09638A824F56BDA00F2062F /* Box.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638A524F56BDA00F2062F /* Box.swift */; };
		A09638AA24F5718C00F2062F /* DataModel.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = A02C584D24F325A400C82D68 /* DataModel.xcdatamodeld */; };
		A09638AC24F5746600F2062F /* DataTransferManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638AB24F5746600F2062F /* DataTransferManager.swift */; };
		A09638AE24F5762700F2062F /* DataTransferManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638AD24F5762700F2062F /* DataTransferManager.swift */; };
		A09638AF24F5802800F2062F /* Server+CoreData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096389924F56A5100F2062F /* Server+CoreData.swift */; };
		A09638B124F5804100F2062F /* Server+Serialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638B024F5804100F2062F /* Server+Serialization.swift */; };
		A09638B324F5804100F2062F /* Server+Serialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638B024F5804100F2062F /* Server+Serialization.swift */; };
		A09638B724F58BD300F2062F /* ServerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638B624F58BD300F2062F /* ServerView.swift */; };
		A09656AC24F2C8E8009D0FE1 /* PreviewMockData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09656AB24F2C8E8009D0FE1 /* PreviewMockData.swift */; };
		A09656AD24F2C8E8009D0FE1 /* PreviewMockData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09656AB24F2C8E8009D0FE1 /* PreviewMockData.swift */; };
		A0AAF2AA257BB39600A2217A /* NoTorrentsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0AAF2A9257BB39600A2217A /* NoTorrentsView.swift */; };
		A0AAF2BB257BB44A00A2217A /* NoTorrentsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0AAF2A9257BB39600A2217A /* NoTorrentsView.swift */; };
		A0AAF2C4257BB44B00A2217A /* NoTorrentsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0AAF2A9257BB39600A2217A /* NoTorrentsView.swift */; };
		A0AAF2C5257BB44C00A2217A /* NoTorrentsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0AAF2A9257BB39600A2217A /* NoTorrentsView.swift */; };
		A0AC7BB92E710A79004C65AF /* FloatingServerStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0AC7BB82E710A79004C65AF /* FloatingServerStatusView.swift */; };
		A0B8C13324F3FF4E00ACD713 /* DocumentPickerAdapter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B8C13224F3FF4E00ACD713 /* DocumentPickerAdapter.swift */; };
		A0B9B8082516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B9B8072516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift */; };
		A0B9B8092516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B9B8072516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift */; };
		A0B9B8442516369600E3486A /* CoreDataManagedObjectDeleter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3B24F436A400C76682 /* CoreDataManagedObjectDeleter.swift */; };
		A0B9B84D2516369600E3486A /* CoreDataManagedObjectDeleter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3B24F436A400C76682 /* CoreDataManagedObjectDeleter.swift */; };
		A0B9B85F2516373300E3486A /* Box+View.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B9B85E2516373300E3486A /* Box+View.swift */; };
		A0B9B88B251637D500E3486A /* Box+View.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B9B88A251637D500E3486A /* Box+View.swift */; };
		A0B9B89E2516380C00E3486A /* Box+View.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B9B89D2516380C00E3486A /* Box+View.swift */; };
		A0B9B8AF2516384300E3486A /* CoreDataManagedObjectDeleter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3B24F436A400C76682 /* CoreDataManagedObjectDeleter.swift */; };
		A0B9B8B82516385F00E3486A /* MockCoreDataManagedObjectDeleter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B9B8072516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift */; };
		A0B9B8C32516388000E3486A /* Box+View.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0B9B8C22516388000E3486A /* Box+View.swift */; };
		A0BD79AC270B71CB00E05093 /* RemoteServerSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0BD79AB270B71CB00E05093 /* RemoteServerSettingsView.swift */; };
		A0BD79AF270B73C000E05093 /* RemoteServerSettingsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0BD79AE270B73BF00E05093 /* RemoteServerSettingsPresenter.swift */; };
		A0BD79B0270B73C000E05093 /* RemoteServerSettingsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0BD79AE270B73BF00E05093 /* RemoteServerSettingsPresenter.swift */; };
		A0C071AD24F59CC700CEC1DA /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071AC24F59CC700CEC1DA /* MainView.swift */; };
		A0C071AF24F59CC800CEC1DA /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A0C071AE24F59CC800CEC1DA /* Assets.xcassets */; };
		A0C071B224F59CC800CEC1DA /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A0C071B124F59CC800CEC1DA /* Preview Assets.xcassets */; };
		A0C071BD24F59CC800CEC1DA /* SeedTruckTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071BC24F59CC800CEC1DA /* SeedTruckTests.swift */; };
		A0C071D424F5A03500CEC1DA /* SettingsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3524F42E4100C76682 /* SettingsPresenter.swift */; };
		A0C071D524F5A03500CEC1DA /* ProgressBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E824F205B100B3B1CD /* ProgressBarView.swift */; };
		A0C071D624F5A03500CEC1DA /* TorrentDetailsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A073DD2324F4768400BA37C9 /* TorrentDetailsPresenter.swift */; };
		A0C071D724F5A04700CEC1DA /* Server.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585524F3400200C82D68 /* Server.swift */; };
		A0C071D924F5A04700CEC1DA /* Server+CoreData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096389924F56A5100F2062F /* Server+CoreData.swift */; };
		A0C071DA24F5A05300CEC1DA /* Transmission+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6F24F30044002A78B5 /* Transmission+Extensions.swift */; };
		A0C071DC24F5A05300CEC1DA /* Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A07FFC6C24F2F34E002A78B5 /* Transmission.swift */; };
		A0C071DD24F5A05300CEC1DA /* RemoteTorrent+Transmission.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585124F326EA00C82D68 /* RemoteTorrent+Transmission.swift */; };
		A0C071DF24F5A05300CEC1DA /* RemoteTorrent.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7E424F20C8D009D72C6 /* RemoteTorrent.swift */; };
		A0C071E024F5A05300CEC1DA /* ServerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = A096388624F569CA00F2062F /* ServerType.swift */; };
		A0C071E224F5A05900CEC1DA /* ByteCountFormatter+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A071CAC824F3113800B6AC0C /* ByteCountFormatter+Extensions.swift */; };
		A0C071E324F5A05900CEC1DA /* ServerConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7EE24F21078009D72C6 /* ServerConnection.swift */; };
		A0C071E624F5A05900CEC1DA /* TransmissionConnection.swift in Sources */ = {isa = PBXBuildFile; fileRef = A011C7F124F21088009D72C6 /* TransmissionConnection.swift */; };
		A0C071E824F5A05900CEC1DA /* PreviewMockData.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09656AB24F2C8E8009D0FE1 /* PreviewMockData.swift */; };
		A0C071E924F5A05900CEC1DA /* String+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A01056AA24F491B600DA056D /* String+Extensions.swift */; };
		A0C071EA24F5A06100CEC1DA /* NewServerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A02C585824F3446100C82D68 /* NewServerView.swift */; };
		A0C071EE24F5A06100CEC1DA /* ErrorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16524F43A7300B57E45 /* ErrorView.swift */; };
		A0C071EF24F5A06100CEC1DA /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A023BAE624F294EF00B82FB3 /* SettingsView.swift */; };
		A0C071F024F5A06100CEC1DA /* Box.swift in Sources */ = {isa = PBXBuildFile; fileRef = A09638A524F56BDA00F2062F /* Box.swift */; };
		A0C071F124F5A06100CEC1DA /* NoServersConfiguredView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16224F43A5A00B57E45 /* NoServersConfiguredView.swift */; };
		A0C071F324F5A06100CEC1DA /* TorrentDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A023BAE324F294E400B82FB3 /* TorrentDetailsView.swift */; };
		A0C071F424F5A06100CEC1DA /* TorrentItemView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A045C8E524F2021100B3B1CD /* TorrentItemView.swift */; };
		A0C071F624F5A06100CEC1DA /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A05FE16924F45B2700B57E45 /* LoadingView.swift */; };
		A0C071F724F5A15D00CEC1DA /* TorrentListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3824F4358500C76682 /* TorrentListView.swift */; };
		A0C071F824F5A17C00CEC1DA /* SeedTruckApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071AA24F59CC700CEC1DA /* SeedTruckApp.swift */; };
		A0C071FA24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071F924F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift */; };
		A0C071FB24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071F924F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift */; };
		A0C071FC24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071F924F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift */; };
		A0C071FD24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071F924F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift */; };
		A0C071FF24F5AFC400CEC1DA /* ServerStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071FE24F5AFC400CEC1DA /* ServerStatusView.swift */; };
		A0C0720024F5AFC400CEC1DA /* ServerStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071FE24F5AFC400CEC1DA /* ServerStatusView.swift */; };
		A0C0720424F5B03700CEC1DA /* ServerStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C0720324F5B03700CEC1DA /* ServerStatusView.swift */; };
		A0C0720524F5B04500CEC1DA /* ServerStatusView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C071FE24F5AFC400CEC1DA /* ServerStatusView.swift */; };
		A0C0720624F5B90300CEC1DA /* DataModel.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = A02C584D24F325A400C82D68 /* DataModel.xcdatamodeld */; };
		A0C1DB3624F42E4100C76682 /* SettingsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3524F42E4100C76682 /* SettingsPresenter.swift */; };
		A0C1DB3724F42E4100C76682 /* SettingsPresenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3524F42E4100C76682 /* SettingsPresenter.swift */; };
		A0C1DB3924F4358500C76682 /* TorrentListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3824F4358500C76682 /* TorrentListView.swift */; };
		A0C1DB3A24F4358500C76682 /* TorrentListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0C1DB3824F4358500C76682 /* TorrentListView.swift */; };
		A0CE00F725853C53002777BF /* TorrentsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE00F625853C53002777BF /* TorrentsView.swift */; };
		A0CE011125853D93002777BF /* MenuItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE011025853D93002777BF /* MenuItem.swift */; };
		A0CE011225853D93002777BF /* MenuItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE011025853D93002777BF /* MenuItem.swift */; };
		A0CE011C25853DD2002777BF /* TorrentsView+Shared.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE011B25853DD2002777BF /* TorrentsView+Shared.swift */; };
		A0CE011D25853DD2002777BF /* TorrentsView+Shared.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE011B25853DD2002777BF /* TorrentsView+Shared.swift */; };
		A0CE013725854916002777BF /* ServerDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE013625854916002777BF /* ServerDetailsView.swift */; };
		A0CE013825854916002777BF /* ServerDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE013625854916002777BF /* ServerDetailsView.swift */; };
		A0CE01422585494A002777BF /* ConnectionResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE01412585494A002777BF /* ConnectionResult.swift */; };
		A0CE01432585494A002777BF /* ConnectionResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE01412585494A002777BF /* ConnectionResult.swift */; };
		A0CE01442585494A002777BF /* ConnectionResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE01412585494A002777BF /* ConnectionResult.swift */; };
		A0CE016E25854D44002777BF /* NotificationName+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE016D25854D44002777BF /* NotificationName+Extensions.swift */; };
		A0CE016F25854D44002777BF /* NotificationName+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE016D25854D44002777BF /* NotificationName+Extensions.swift */; };
		A0CE017025854D44002777BF /* NotificationName+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE016D25854D44002777BF /* NotificationName+Extensions.swift */; };
		A0CE018325855834002777BF /* NewServerView+Shared.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE018225855834002777BF /* NewServerView+Shared.swift */; };
		A0CE018425855834002777BF /* NewServerView+Shared.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE018225855834002777BF /* NewServerView+Shared.swift */; };
		A0CE018525855834002777BF /* NewServerView+Shared.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE018225855834002777BF /* NewServerView+Shared.swift */; };
		A0CE01A725855871002777BF /* NewServerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE01A625855871002777BF /* NewServerView.swift */; };
		A0CE01C1258559DD002777BF /* NewServerDoneView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE01C0258559DD002777BF /* NewServerDoneView.swift */; };
		A0CE01CA25855BA1002777BF /* NotificationName+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE016D25854D44002777BF /* NotificationName+Extensions.swift */; };
		A0CE01FC25857C9D002777BF /* TorrentHandlerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE01FB25857C9D002777BF /* TorrentHandlerView.swift */; };
		A0CE020625857E0C002777BF /* TorrentHandlerView+Shared.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE020525857E0C002777BF /* TorrentHandlerView+Shared.swift */; };
		A0CE020725857E0C002777BF /* TorrentHandlerView+Shared.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CE020525857E0C002777BF /* TorrentHandlerView+Shared.swift */; };
		A0D87FF1258456260031E898 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0D87FF0258456260031E898 /* SettingsView.swift */; };
		A0D88003258457300031E898 /* GeneralSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0D88002258457300031E898 /* GeneralSettingsView.swift */; };
		A0D8800D258457370031E898 /* ServerSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0D8800C258457370031E898 /* ServerSettingsView.swift */; };
		A0D93F242E70CD670041FE7B /* AppIcon.icon in Resources */ = {isa = PBXBuildFile; fileRef = A0D93F232E70CD670041FE7B /* AppIcon.icon */; };
		A0D93F252E70CD670041FE7B /* AppIcon.icon in Resources */ = {isa = PBXBuildFile; fileRef = A0D93F232E70CD670041FE7B /* AppIcon.icon */; };
		A0D93F262E70CD670041FE7B /* AppIcon.icon in Resources */ = {isa = PBXBuildFile; fileRef = A0D93F232E70CD670041FE7B /* AppIcon.icon */; };
		A0D93F272E70CD670041FE7B /* AppIcon.icon in Resources */ = {isa = PBXBuildFile; fileRef = A0D93F232E70CD670041FE7B /* AppIcon.icon */; };
		A0E7426525154FCB0029FFD6 /* SharedBucket.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E7426425154FCB0029FFD6 /* SharedBucket.swift */; };
		A0E7426625154FCB0029FFD6 /* SharedBucket.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E7426425154FCB0029FFD6 /* SharedBucket.swift */; };
		A0E7426725154FCB0029FFD6 /* SharedBucket.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E7426425154FCB0029FFD6 /* SharedBucket.swift */; };
		A0E7426825154FCB0029FFD6 /* SharedBucket.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E7426425154FCB0029FFD6 /* SharedBucket.swift */; };
		A0E7427A2515501F0029FFD6 /* DataTransferManageable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E742792515501F0029FFD6 /* DataTransferManageable.swift */; };
		A0E7427C2515501F0029FFD6 /* DataTransferManageable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E742792515501F0029FFD6 /* DataTransferManageable.swift */; };
		A0E7427D2515501F0029FFD6 /* DataTransferManageable.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0E742792515501F0029FFD6 /* DataTransferManageable.swift */; };
		A0F99C132562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99C122562028C002D7BC6 /* NSPersistentContainer+Extensions.swift */; };
		A0F99C142562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99C122562028C002D7BC6 /* NSPersistentContainer+Extensions.swift */; };
		A0F99C152562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99C122562028C002D7BC6 /* NSPersistentContainer+Extensions.swift */; };
		A0F99C162562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99C122562028C002D7BC6 /* NSPersistentContainer+Extensions.swift */; };
		A0F99C2825620933002D7BC6 /* UTI.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99C2725620933002D7BC6 /* UTI.swift */; };
		A0F99C2925620933002D7BC6 /* UTI.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99C2725620933002D7BC6 /* UTI.swift */; };
		A0F99C3425620981002D7BC6 /* TorrentFile.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99C3325620981002D7BC6 /* TorrentFile.swift */; };
		A0F99D152569EAD6002D7BC6 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A0F99D142569EACF002D7BC6 /* Assets.xcassets */; };
		A0F99D512569F676002D7BC6 /* Application.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0F99D3F2569F666002D7BC6 /* Application.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A045C8BA24F2006500B3B1CD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A045C89D24F2006400B3B1CD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A045C8A824F2006500B3B1CD;
			remoteInfo = "SeedTruck (iOS)";
		};
		A045C8C524F2006500B3B1CD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A045C89D24F2006400B3B1CD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A045C8B024F2006500B3B1CD;
			remoteInfo = "SeedTruck (macOS)";
		};
		A096387724F568D000F2062F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A045C89D24F2006400B3B1CD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A096385724F568CF00F2062F;
			remoteInfo = "SeedTruck (watchOS)";
		};
		A0C071B924F59CC800CEC1DA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A045C89D24F2006400B3B1CD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A0C071A724F59CC700CEC1DA;
			remoteInfo = SeedTruck;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		A096387A24F568D000F2062F /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				A096387924F568D000F2062F /* SeedTruck (watchOS).app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		A0001B6E270B879400F40C2F /* RemoteServerSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteServerSettingsView.swift; sourceTree = "<group>"; };
		A01056A424F48D5E00DA056D /* TorrentHandlerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentHandlerView.swift; sourceTree = "<group>"; };
		A01056A724F4901700DA056D /* LocalTorrent+Initializers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "LocalTorrent+Initializers.swift"; sourceTree = "<group>"; };
		A01056AA24F491B600DA056D /* String+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "String+Extensions.swift"; sourceTree = "<group>"; };
		A011C7E424F20C8D009D72C6 /* RemoteTorrent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteTorrent.swift; sourceTree = "<group>"; };
		A011C7EE24F21078009D72C6 /* ServerConnection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServerConnection.swift; sourceTree = "<group>"; };
		A011C7F124F21088009D72C6 /* TransmissionConnection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransmissionConnection.swift; sourceTree = "<group>"; };
		A011C7F424F211C6009D72C6 /* LocalTorrent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocalTorrent.swift; sourceTree = "<group>"; };
		A011C7F724F2130A009D72C6 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		A011C7FA24F21406009D72C6 /* LICENSE.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = LICENSE.txt; sourceTree = "<group>"; };
		A012561225046F0D00C7D51B /* SeedTruckApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeedTruckApp.swift; sourceTree = "<group>"; };
		A023BAE324F294E400B82FB3 /* TorrentDetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentDetailsView.swift; sourceTree = "<group>"; };
		A023BAE624F294EF00B82FB3 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A02C584E24F325A400C82D68 /* DataModel.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = DataModel.xcdatamodel; sourceTree = "<group>"; };
		A02C585124F326EA00C82D68 /* RemoteTorrent+Transmission.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "RemoteTorrent+Transmission.swift"; sourceTree = "<group>"; };
		A02C585524F3400200C82D68 /* Server.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Server.swift; sourceTree = "<group>"; };
		A02C585824F3446100C82D68 /* NewServerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewServerView.swift; sourceTree = "<group>"; };
		A045C8A224F2006400B3B1CD /* SeedTruckApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeedTruckApp.swift; sourceTree = "<group>"; };
		A045C8A424F2006500B3B1CD /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A045C8A924F2006500B3B1CD /* SeedTruck.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SeedTruck.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A045C8AC24F2006500B3B1CD /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A045C8B124F2006500B3B1CD /* SeedTruck.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SeedTruck.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A045C8B324F2006500B3B1CD /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A045C8B424F2006500B3B1CD /* macOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = macOS.entitlements; sourceTree = "<group>"; };
		A045C8B924F2006500B3B1CD /* Tests iOS.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Tests iOS.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		A045C8BD24F2006500B3B1CD /* Tests_iOS.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Tests_iOS.swift; sourceTree = "<group>"; };
		A045C8BF24F2006500B3B1CD /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A045C8C424F2006500B3B1CD /* Tests macOS.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Tests macOS.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		A045C8C824F2006500B3B1CD /* Tests_macOS.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Tests_macOS.swift; sourceTree = "<group>"; };
		A045C8CA24F2006500B3B1CD /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A045C8DF24F2011E00B3B1CD /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		A045C8E224F2013100B3B1CD /* TorrentsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentsView.swift; sourceTree = "<group>"; };
		A045C8E524F2021100B3B1CD /* TorrentItemView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentItemView.swift; sourceTree = "<group>"; };
		A045C8E824F205B100B3B1CD /* ProgressBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressBarView.swift; sourceTree = "<group>"; };
		A051D54C2E563EF800A94EDA /* LabelPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LabelPickerView.swift; sourceTree = "<group>"; };
		A0589FEC251799F30022D839 /* BinaryInteger+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "BinaryInteger+Extensions.swift"; sourceTree = "<group>"; };
		A05FE16224F43A5A00B57E45 /* NoServersConfiguredView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoServersConfiguredView.swift; sourceTree = "<group>"; };
		A05FE16524F43A7300B57E45 /* ErrorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ErrorView.swift; sourceTree = "<group>"; };
		A05FE16924F45B2700B57E45 /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		A06DEFA324FC8219004B2DC9 /* NoServersConfiguredView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoServersConfiguredView.swift; sourceTree = "<group>"; };
		A06DEFA724FD7EF5004B2DC9 /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		A071CAC824F3113800B6AC0C /* ByteCountFormatter+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ByteCountFormatter+Extensions.swift"; sourceTree = "<group>"; };
		A071CC6D24FBC9BF00E46065 /* Style.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Style.swift; sourceTree = "<group>"; };
		A073DD2024F4658700BA37C9 /* AddMagnetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddMagnetView.swift; sourceTree = "<group>"; };
		A073DD2324F4768400BA37C9 /* TorrentDetailsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentDetailsPresenter.swift; sourceTree = "<group>"; };
		A07DD0BE2512C96600559D9A /* Connectable.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Connectable.swift; sourceTree = "<group>"; };
		A07DD0F32512C9D400559D9A /* TemporaryServer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TemporaryServer.swift; sourceTree = "<group>"; };
		A07DD1002512C9E900559D9A /* ConnectionDetails.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectionDetails.swift; sourceTree = "<group>"; };
		A07FFC6C24F2F34E002A78B5 /* Transmission.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Transmission.swift; sourceTree = "<group>"; };
		A07FFC6F24F30044002A78B5 /* Transmission+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Transmission+Extensions.swift"; sourceTree = "<group>"; };
		A07FFC7224F306AC002A78B5 /* TransmissionModelTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransmissionModelTests.swift; sourceTree = "<group>"; };
		A08863A42585ADD800443ECB /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		A08863C92585B65E00443ECB /* View+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "View+Extensions.swift"; sourceTree = "<group>"; };
		A08864062585BB4100443ECB /* GeneralSettingsView+AutoUpdateInterval.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "GeneralSettingsView+AutoUpdateInterval.swift"; sourceTree = "<group>"; };
		A096385824F568CF00F2062F /* SeedTruck (watchOS).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "SeedTruck (watchOS).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A096385A24F568CF00F2062F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A096385C24F568CF00F2062F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A096386624F568CF00F2062F /* SeedTruckApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeedTruckApp.swift; sourceTree = "<group>"; };
		A096386824F568CF00F2062F /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		A096387324F568D000F2062F /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A096388624F569CA00F2062F /* ServerType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServerType.swift; sourceTree = "<group>"; };
		A096388F24F56A1200F2062F /* LocalTorrent+ComputedProperties.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "LocalTorrent+ComputedProperties.swift"; sourceTree = "<group>"; };
		A096389924F56A5100F2062F /* Server+CoreData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Server+CoreData.swift"; sourceTree = "<group>"; };
		A09638A524F56BDA00F2062F /* Box.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Box.swift; sourceTree = "<group>"; };
		A09638AB24F5746600F2062F /* DataTransferManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataTransferManager.swift; sourceTree = "<group>"; };
		A09638AD24F5762700F2062F /* DataTransferManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataTransferManager.swift; sourceTree = "<group>"; };
		A09638B024F5804100F2062F /* Server+Serialization.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Server+Serialization.swift"; sourceTree = "<group>"; };
		A09638B624F58BD300F2062F /* ServerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServerView.swift; sourceTree = "<group>"; };
		A09656AB24F2C8E8009D0FE1 /* PreviewMockData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreviewMockData.swift; sourceTree = "<group>"; };
		A0AAF2A9257BB39600A2217A /* NoTorrentsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoTorrentsView.swift; sourceTree = "<group>"; };
		A0AC7BB82E710A79004C65AF /* FloatingServerStatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FloatingServerStatusView.swift; sourceTree = "<group>"; };
		A0B8C13224F3FF4E00ACD713 /* DocumentPickerAdapter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentPickerAdapter.swift; sourceTree = "<group>"; };
		A0B9B8072516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MockCoreDataManagedObjectDeleter.swift; sourceTree = "<group>"; };
		A0B9B85E2516373300E3486A /* Box+View.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Box+View.swift"; sourceTree = "<group>"; };
		A0B9B88A251637D500E3486A /* Box+View.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Box+View.swift"; sourceTree = "<group>"; };
		A0B9B89D2516380C00E3486A /* Box+View.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Box+View.swift"; sourceTree = "<group>"; };
		A0B9B8C22516388000E3486A /* Box+View.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Box+View.swift"; sourceTree = "<group>"; };
		A0BD79AB270B71CB00E05093 /* RemoteServerSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteServerSettingsView.swift; sourceTree = "<group>"; };
		A0BD79AE270B73BF00E05093 /* RemoteServerSettingsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteServerSettingsPresenter.swift; sourceTree = "<group>"; };
		A0C071A824F59CC700CEC1DA /* SeedTruck (tvOS).app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "SeedTruck (tvOS).app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A0C071AA24F59CC700CEC1DA /* SeedTruckApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeedTruckApp.swift; sourceTree = "<group>"; };
		A0C071AC24F59CC700CEC1DA /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		A0C071AE24F59CC800CEC1DA /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A0C071B124F59CC800CEC1DA /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A0C071B324F59CC800CEC1DA /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A0C071B824F59CC800CEC1DA /* Tests tvOS.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Tests tvOS.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		A0C071BC24F59CC800CEC1DA /* SeedTruckTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeedTruckTests.swift; sourceTree = "<group>"; };
		A0C071BE24F59CC800CEC1DA /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A0C071F924F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "RemoteTorrent+Convenience.swift"; sourceTree = "<group>"; };
		A0C071FE24F5AFC400CEC1DA /* ServerStatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServerStatusView.swift; sourceTree = "<group>"; };
		A0C0720324F5B03700CEC1DA /* ServerStatusView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServerStatusView.swift; sourceTree = "<group>"; };
		A0C1DB3524F42E4100C76682 /* SettingsPresenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsPresenter.swift; sourceTree = "<group>"; };
		A0C1DB3824F4358500C76682 /* TorrentListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentListView.swift; sourceTree = "<group>"; };
		A0C1DB3B24F436A400C76682 /* CoreDataManagedObjectDeleter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoreDataManagedObjectDeleter.swift; sourceTree = "<group>"; };
		A0CE00F625853C53002777BF /* TorrentsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentsView.swift; sourceTree = "<group>"; };
		A0CE011025853D93002777BF /* MenuItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuItem.swift; sourceTree = "<group>"; };
		A0CE011B25853DD2002777BF /* TorrentsView+Shared.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "TorrentsView+Shared.swift"; sourceTree = "<group>"; };
		A0CE013625854916002777BF /* ServerDetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServerDetailsView.swift; sourceTree = "<group>"; };
		A0CE01412585494A002777BF /* ConnectionResult.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConnectionResult.swift; sourceTree = "<group>"; };
		A0CE016D25854D44002777BF /* NotificationName+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NotificationName+Extensions.swift"; sourceTree = "<group>"; };
		A0CE018225855834002777BF /* NewServerView+Shared.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NewServerView+Shared.swift"; sourceTree = "<group>"; };
		A0CE01A625855871002777BF /* NewServerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewServerView.swift; sourceTree = "<group>"; };
		A0CE01C0258559DD002777BF /* NewServerDoneView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewServerDoneView.swift; sourceTree = "<group>"; };
		A0CE01FB25857C9D002777BF /* TorrentHandlerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentHandlerView.swift; sourceTree = "<group>"; };
		A0CE020525857E0C002777BF /* TorrentHandlerView+Shared.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "TorrentHandlerView+Shared.swift"; sourceTree = "<group>"; };
		A0D87FF0258456260031E898 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A0D88002258457300031E898 /* GeneralSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeneralSettingsView.swift; sourceTree = "<group>"; };
		A0D8800C258457370031E898 /* ServerSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServerSettingsView.swift; sourceTree = "<group>"; };
		A0D93F232E70CD670041FE7B /* AppIcon.icon */ = {isa = PBXFileReference; lastKnownFileType = folder.iconcomposer.icon; path = AppIcon.icon; sourceTree = "<group>"; };
		A0E7426425154FCB0029FFD6 /* SharedBucket.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedBucket.swift; sourceTree = "<group>"; };
		A0E742792515501F0029FFD6 /* DataTransferManageable.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataTransferManageable.swift; sourceTree = "<group>"; };
		A0F99C122562028C002D7BC6 /* NSPersistentContainer+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NSPersistentContainer+Extensions.swift"; sourceTree = "<group>"; };
		A0F99C2725620933002D7BC6 /* UTI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UTI.swift; sourceTree = "<group>"; };
		A0F99C3325620981002D7BC6 /* TorrentFile.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TorrentFile.swift; sourceTree = "<group>"; };
		A0F99D142569EACF002D7BC6 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A0F99D3F2569F666002D7BC6 /* Application.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Application.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A045C8A624F2006500B3B1CD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A022E6522C8FBC7200BAEED3 /* SwiftyBencode in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8AE24F2006500B3B1CD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A022E6542C8FBC8400BAEED3 /* SwiftyBencode in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8B624F2006500B3B1CD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8C124F2006500B3B1CD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A096385E24F568CF00F2062F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A022E6562C8FBC9000BAEED3 /* SwiftyBencode in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A0C071A524F59CC700CEC1DA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A022E6582C8FBCCE00BAEED3 /* SwiftyBencode in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A0C071B524F59CC800CEC1DA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A011C7E324F20C78009D72C6 /* Models */ = {
			isa = PBXGroup;
			children = (
				A02C585424F33FEE00C82D68 /* Core Data */,
				A07FFC6824F2F1BB002A78B5 /* Remote */,
				A011C7ED24F21064009D72C6 /* Network */,
				A07DD1002512C9E900559D9A /* ConnectionDetails.swift */,
				A0CE01412585494A002777BF /* ConnectionResult.swift */,
				A011C7F424F211C6009D72C6 /* LocalTorrent.swift */,
				A096388F24F56A1200F2062F /* LocalTorrent+ComputedProperties.swift */,
				A01056A724F4901700DA056D /* LocalTorrent+Initializers.swift */,
				A011C7E424F20C8D009D72C6 /* RemoteTorrent.swift */,
				A0C071F924F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift */,
				A02C585124F326EA00C82D68 /* RemoteTorrent+Transmission.swift */,
				A096388624F569CA00F2062F /* ServerType.swift */,
				A07DD0F32512C9D400559D9A /* TemporaryServer.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A011C7ED24F21064009D72C6 /* Network */ = {
			isa = PBXGroup;
			children = (
				A011C7EE24F21078009D72C6 /* ServerConnection.swift */,
				A011C7F124F21088009D72C6 /* TransmissionConnection.swift */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		A02C585424F33FEE00C82D68 /* Core Data */ = {
			isa = PBXGroup;
			children = (
				A02C585524F3400200C82D68 /* Server.swift */,
				A096389924F56A5100F2062F /* Server+CoreData.swift */,
				A09638B024F5804100F2062F /* Server+Serialization.swift */,
			);
			path = "Core Data";
			sourceTree = "<group>";
		};
		A045C89C24F2006400B3B1CD = {
			isa = PBXGroup;
			children = (
				A011C7FA24F21406009D72C6 /* LICENSE.txt */,
				A011C7F724F2130A009D72C6 /* README.md */,
				A045C8A124F2006400B3B1CD /* Shared */,
				A045C8AB24F2006500B3B1CD /* iOS */,
				A045C8B224F2006500B3B1CD /* macOS */,
				A096385924F568CF00F2062F /* watchOS */,
				A0C071A924F59CC700CEC1DA /* tvOS */,
				A045C8BC24F2006500B3B1CD /* Tests iOS */,
				A045C8C724F2006500B3B1CD /* Tests macOS */,
				A0C071BB24F59CC800CEC1DA /* Tests tvOS */,
				A045C8AA24F2006500B3B1CD /* Products */,
				A054DA8524F51E34003A12AD /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		A045C8A124F2006400B3B1CD /* Shared */ = {
			isa = PBXGroup;
			children = (
				A045C8EB24F208AC00B3B1CD /* Components */,
				A011C7E324F20C78009D72C6 /* Models */,
				A0B9B8062516366400E3486A /* Mocks */,
				A0C1DB3E24F4376800C76682 /* Presenters */,
				A07DD11D2512CAB300559D9A /* Protocols */,
				A071CAC724F3110200B6AC0C /* Utilities */,
				A0C1DB3F24F4376D00C76682 /* Views */,
				A02C584D24F325A400C82D68 /* DataModel.xcdatamodeld */,
				A0D93F232E70CD670041FE7B /* AppIcon.icon */,
			);
			path = Shared;
			sourceTree = "<group>";
		};
		A045C8AA24F2006500B3B1CD /* Products */ = {
			isa = PBXGroup;
			children = (
				A045C8A924F2006500B3B1CD /* SeedTruck.app */,
				A045C8B124F2006500B3B1CD /* SeedTruck.app */,
				A045C8B924F2006500B3B1CD /* Tests iOS.xctest */,
				A045C8C424F2006500B3B1CD /* Tests macOS.xctest */,
				A096385824F568CF00F2062F /* SeedTruck (watchOS).app */,
				A0C071A824F59CC700CEC1DA /* SeedTruck (tvOS).app */,
				A0C071B824F59CC800CEC1DA /* Tests tvOS.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A045C8AB24F2006500B3B1CD /* iOS */ = {
			isa = PBXGroup;
			children = (
				A0B9B880251637BE00E3486A /* Components */,
				A06DEFA224FC7F2F004B2DC9 /* Utilities */,
				A06DEFA624FD7EDC004B2DC9 /* Views */,
				A045C8A224F2006400B3B1CD /* SeedTruckApp.swift */,
				A045C8A424F2006500B3B1CD /* Assets.xcassets */,
				A045C8AC24F2006500B3B1CD /* Info.plist */,
			);
			path = iOS;
			sourceTree = "<group>";
		};
		A045C8B224F2006500B3B1CD /* macOS */ = {
			isa = PBXGroup;
			children = (
				A0B9B8C12516387100E3486A /* Components */,
				A0F99C3225620974002D7BC6 /* Models */,
				A0F99D3E2569F654002D7BC6 /* Utilities */,
				A06DEFA524FD7ED9004B2DC9 /* Views */,
				A0CE00F625853C53002777BF /* TorrentsView.swift */,
				A012561225046F0D00C7D51B /* SeedTruckApp.swift */,
				A0F99D142569EACF002D7BC6 /* Assets.xcassets */,
				A045C8B324F2006500B3B1CD /* Info.plist */,
				A045C8B424F2006500B3B1CD /* macOS.entitlements */,
			);
			path = macOS;
			sourceTree = "<group>";
		};
		A045C8BC24F2006500B3B1CD /* Tests iOS */ = {
			isa = PBXGroup;
			children = (
				A045C8BD24F2006500B3B1CD /* Tests_iOS.swift */,
				A07FFC7224F306AC002A78B5 /* TransmissionModelTests.swift */,
				A045C8BF24F2006500B3B1CD /* Info.plist */,
			);
			path = "Tests iOS";
			sourceTree = "<group>";
		};
		A045C8C724F2006500B3B1CD /* Tests macOS */ = {
			isa = PBXGroup;
			children = (
				A045C8C824F2006500B3B1CD /* Tests_macOS.swift */,
				A045C8CA24F2006500B3B1CD /* Info.plist */,
			);
			path = "Tests macOS";
			sourceTree = "<group>";
		};
		A045C8EB24F208AC00B3B1CD /* Components */ = {
			isa = PBXGroup;
			children = (
				A09638A524F56BDA00F2062F /* Box.swift */,
				A045C8E824F205B100B3B1CD /* ProgressBarView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		A054DA8524F51E34003A12AD /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A06DEFA224FC7F2F004B2DC9 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A09638AD24F5762700F2062F /* DataTransferManager.swift */,
				A0B8C13224F3FF4E00ACD713 /* DocumentPickerAdapter.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		A06DEFA524FD7ED9004B2DC9 /* Views */ = {
			isa = PBXGroup;
			children = (
				A0D880162584573B0031E898 /* Settings */,
				A06DEFA724FD7EF5004B2DC9 /* MainView.swift */,
				A0CE01FB25857C9D002777BF /* TorrentHandlerView.swift */,
				A0001B6E270B879400F40C2F /* RemoteServerSettingsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A06DEFA624FD7EDC004B2DC9 /* Views */ = {
			isa = PBXGroup;
			children = (
				A045C8DF24F2011E00B3B1CD /* MainView.swift */,
				A045C8E224F2013100B3B1CD /* TorrentsView.swift */,
				A01056A424F48D5E00DA056D /* TorrentHandlerView.swift */,
				A0BD79AB270B71CB00E05093 /* RemoteServerSettingsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A071CAC724F3110200B6AC0C /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A0589FEC251799F30022D839 /* BinaryInteger+Extensions.swift */,
				A071CAC824F3113800B6AC0C /* ByteCountFormatter+Extensions.swift */,
				A08863A42585ADD800443ECB /* Constants.swift */,
				A0C1DB3B24F436A400C76682 /* CoreDataManagedObjectDeleter.swift */,
				A0CE011025853D93002777BF /* MenuItem.swift */,
				A0CE016D25854D44002777BF /* NotificationName+Extensions.swift */,
				A0F99C122562028C002D7BC6 /* NSPersistentContainer+Extensions.swift */,
				A0E7426425154FCB0029FFD6 /* SharedBucket.swift */,
				A01056AA24F491B600DA056D /* String+Extensions.swift */,
				A071CC6D24FBC9BF00E46065 /* Style.swift */,
				A0F99C2725620933002D7BC6 /* UTI.swift */,
				A08863C92585B65E00443ECB /* View+Extensions.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		A07DD11D2512CAB300559D9A /* Protocols */ = {
			isa = PBXGroup;
			children = (
				A07DD0BE2512C96600559D9A /* Connectable.swift */,
				A0E742792515501F0029FFD6 /* DataTransferManageable.swift */,
			);
			path = Protocols;
			sourceTree = "<group>";
		};
		A07FFC6824F2F1BB002A78B5 /* Remote */ = {
			isa = PBXGroup;
			children = (
				A07FFC6C24F2F34E002A78B5 /* Transmission.swift */,
				A07FFC6F24F30044002A78B5 /* Transmission+Extensions.swift */,
			);
			path = Remote;
			sourceTree = "<group>";
		};
		A096385924F568CF00F2062F /* watchOS */ = {
			isa = PBXGroup;
			children = (
				A0B9B889251637C900E3486A /* Components */,
				A09638B924F5901000F2062F /* Utilities */,
				A09638B824F5900900F2062F /* Views */,
				A096386624F568CF00F2062F /* SeedTruckApp.swift */,
				A096387224F568D000F2062F /* Preview Content */,
				A096385A24F568CF00F2062F /* Assets.xcassets */,
				A096385C24F568CF00F2062F /* Info.plist */,
			);
			path = watchOS;
			sourceTree = "<group>";
		};
		A096387224F568D000F2062F /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A096387324F568D000F2062F /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A09638B824F5900900F2062F /* Views */ = {
			isa = PBXGroup;
			children = (
				A096386824F568CF00F2062F /* MainView.swift */,
				A06DEFA324FC8219004B2DC9 /* NoServersConfiguredView.swift */,
				A0C0720324F5B03700CEC1DA /* ServerStatusView.swift */,
				A09638B624F58BD300F2062F /* ServerView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A09638B924F5901000F2062F /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A09638AB24F5746600F2062F /* DataTransferManager.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		A0B9B8062516366400E3486A /* Mocks */ = {
			isa = PBXGroup;
			children = (
				A09656AB24F2C8E8009D0FE1 /* PreviewMockData.swift */,
				A0B9B8072516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift */,
			);
			path = Mocks;
			sourceTree = "<group>";
		};
		A0B9B880251637BE00E3486A /* Components */ = {
			isa = PBXGroup;
			children = (
				A0B9B85E2516373300E3486A /* Box+View.swift */,
				A0AC7BB82E710A79004C65AF /* FloatingServerStatusView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		A0B9B889251637C900E3486A /* Components */ = {
			isa = PBXGroup;
			children = (
				A0B9B88A251637D500E3486A /* Box+View.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		A0B9B89C251637FD00E3486A /* Components */ = {
			isa = PBXGroup;
			children = (
				A0B9B89D2516380C00E3486A /* Box+View.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		A0B9B8C12516387100E3486A /* Components */ = {
			isa = PBXGroup;
			children = (
				A0B9B8C22516388000E3486A /* Box+View.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		A0C071A924F59CC700CEC1DA /* tvOS */ = {
			isa = PBXGroup;
			children = (
				A0B9B89C251637FD00E3486A /* Components */,
				A0C071AA24F59CC700CEC1DA /* SeedTruckApp.swift */,
				A0C071AC24F59CC700CEC1DA /* MainView.swift */,
				A0C071AE24F59CC800CEC1DA /* Assets.xcassets */,
				A0C071B324F59CC800CEC1DA /* Info.plist */,
				A0C071B024F59CC800CEC1DA /* Preview Content */,
			);
			path = tvOS;
			sourceTree = "<group>";
		};
		A0C071B024F59CC800CEC1DA /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A0C071B124F59CC800CEC1DA /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A0C071BB24F59CC800CEC1DA /* Tests tvOS */ = {
			isa = PBXGroup;
			children = (
				A0C071BC24F59CC800CEC1DA /* SeedTruckTests.swift */,
				A0C071BE24F59CC800CEC1DA /* Info.plist */,
			);
			path = "Tests tvOS";
			sourceTree = "<group>";
		};
		A0C1DB3E24F4376800C76682 /* Presenters */ = {
			isa = PBXGroup;
			children = (
				A0BD79AE270B73BF00E05093 /* RemoteServerSettingsPresenter.swift */,
				A0C1DB3524F42E4100C76682 /* SettingsPresenter.swift */,
				A073DD2324F4768400BA37C9 /* TorrentDetailsPresenter.swift */,
			);
			path = Presenters;
			sourceTree = "<group>";
		};
		A0C1DB3F24F4376D00C76682 /* Views */ = {
			isa = PBXGroup;
			children = (
				A0CE018125855825002777BF /* Shared Extensions */,
				A073DD2024F4658700BA37C9 /* AddMagnetView.swift */,
				A05FE16524F43A7300B57E45 /* ErrorView.swift */,
				A051D54C2E563EF800A94EDA /* LabelPickerView.swift */,
				A05FE16924F45B2700B57E45 /* LoadingView.swift */,
				A02C585824F3446100C82D68 /* NewServerView.swift */,
				A05FE16224F43A5A00B57E45 /* NoServersConfiguredView.swift */,
				A0AAF2A9257BB39600A2217A /* NoTorrentsView.swift */,
				A0C071FE24F5AFC400CEC1DA /* ServerStatusView.swift */,
				A023BAE624F294EF00B82FB3 /* SettingsView.swift */,
				A023BAE324F294E400B82FB3 /* TorrentDetailsView.swift */,
				A045C8E524F2021100B3B1CD /* TorrentItemView.swift */,
				A0C1DB3824F4358500C76682 /* TorrentListView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A0CE018125855825002777BF /* Shared Extensions */ = {
			isa = PBXGroup;
			children = (
				A0CE018225855834002777BF /* NewServerView+Shared.swift */,
				A0CE011B25853DD2002777BF /* TorrentsView+Shared.swift */,
				A0CE020525857E0C002777BF /* TorrentHandlerView+Shared.swift */,
			);
			path = "Shared Extensions";
			sourceTree = "<group>";
		};
		A0D880162584573B0031E898 /* Settings */ = {
			isa = PBXGroup;
			children = (
				A0D88002258457300031E898 /* GeneralSettingsView.swift */,
				A0CE01A625855871002777BF /* NewServerView.swift */,
				A0D8800C258457370031E898 /* ServerSettingsView.swift */,
				A0D87FF0258456260031E898 /* SettingsView.swift */,
				A0CE013625854916002777BF /* ServerDetailsView.swift */,
				A0CE01C0258559DD002777BF /* NewServerDoneView.swift */,
			);
			path = Settings;
			sourceTree = "<group>";
		};
		A0F99C3225620974002D7BC6 /* Models */ = {
			isa = PBXGroup;
			children = (
				A0F99C3325620981002D7BC6 /* TorrentFile.swift */,
				A08864062585BB4100443ECB /* GeneralSettingsView+AutoUpdateInterval.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A0F99D3E2569F654002D7BC6 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A0F99D3F2569F666002D7BC6 /* Application.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A045C8A824F2006500B3B1CD /* SeedTruck (iOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A045C8D324F2006500B3B1CD /* Build configuration list for PBXNativeTarget "SeedTruck (iOS)" */;
			buildPhases = (
				A045C8A524F2006500B3B1CD /* Sources */,
				A045C8A624F2006500B3B1CD /* Frameworks */,
				A045C8A724F2006500B3B1CD /* Resources */,
				A096387A24F568D000F2062F /* Embed Watch Content */,
			);
			buildRules = (
			);
			dependencies = (
				A096387824F568D000F2062F /* PBXTargetDependency */,
			);
			name = "SeedTruck (iOS)";
			packageProductDependencies = (
				A022E6512C8FBC7200BAEED3 /* SwiftyBencode */,
			);
			productName = "SeedTruck (iOS)";
			productReference = A045C8A924F2006500B3B1CD /* SeedTruck.app */;
			productType = "com.apple.product-type.application";
		};
		A045C8B024F2006500B3B1CD /* SeedTruck (macOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A045C8D624F2006500B3B1CD /* Build configuration list for PBXNativeTarget "SeedTruck (macOS)" */;
			buildPhases = (
				A045C8AD24F2006500B3B1CD /* Sources */,
				A045C8AE24F2006500B3B1CD /* Frameworks */,
				A045C8AF24F2006500B3B1CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SeedTruck (macOS)";
			packageProductDependencies = (
				A022E6532C8FBC8400BAEED3 /* SwiftyBencode */,
			);
			productName = "SeedTruck (macOS)";
			productReference = A045C8B124F2006500B3B1CD /* SeedTruck.app */;
			productType = "com.apple.product-type.application";
		};
		A045C8B824F2006500B3B1CD /* Tests iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A045C8D924F2006500B3B1CD /* Build configuration list for PBXNativeTarget "Tests iOS" */;
			buildPhases = (
				A045C8B524F2006500B3B1CD /* Sources */,
				A045C8B624F2006500B3B1CD /* Frameworks */,
				A045C8B724F2006500B3B1CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A045C8BB24F2006500B3B1CD /* PBXTargetDependency */,
			);
			name = "Tests iOS";
			productName = "Tests iOS";
			productReference = A045C8B924F2006500B3B1CD /* Tests iOS.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		A045C8C324F2006500B3B1CD /* Tests macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A045C8DC24F2006500B3B1CD /* Build configuration list for PBXNativeTarget "Tests macOS" */;
			buildPhases = (
				A045C8C024F2006500B3B1CD /* Sources */,
				A045C8C124F2006500B3B1CD /* Frameworks */,
				A045C8C224F2006500B3B1CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A045C8C624F2006500B3B1CD /* PBXTargetDependency */,
			);
			name = "Tests macOS";
			productName = "Tests macOS";
			productReference = A045C8C424F2006500B3B1CD /* Tests macOS.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
		A096385724F568CF00F2062F /* SeedTruck (watchOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A096388124F568D000F2062F /* Build configuration list for PBXNativeTarget "SeedTruck (watchOS)" */;
			buildPhases = (
				A096385624F568CF00F2062F /* Resources */,
				A096385D24F568CF00F2062F /* Sources */,
				A096385E24F568CF00F2062F /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SeedTruck (watchOS)";
			productName = "SeedTruck (watchOS)";
			productReference = A096385824F568CF00F2062F /* SeedTruck (watchOS).app */;
			productType = "com.apple.product-type.application";
		};
		A0C071A724F59CC700CEC1DA /* SeedTruck (tvOS) */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A0C071D024F59CC800CEC1DA /* Build configuration list for PBXNativeTarget "SeedTruck (tvOS)" */;
			buildPhases = (
				A0C071A424F59CC700CEC1DA /* Sources */,
				A0C071A524F59CC700CEC1DA /* Frameworks */,
				A0C071A624F59CC700CEC1DA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SeedTruck (tvOS)";
			packageProductDependencies = (
				A022E6572C8FBCCE00BAEED3 /* SwiftyBencode */,
			);
			productName = SeedTruck;
			productReference = A0C071A824F59CC700CEC1DA /* SeedTruck (tvOS).app */;
			productType = "com.apple.product-type.application";
		};
		A0C071B724F59CC800CEC1DA /* Tests tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A0C071D124F59CC800CEC1DA /* Build configuration list for PBXNativeTarget "Tests tvOS" */;
			buildPhases = (
				A0C071B424F59CC800CEC1DA /* Sources */,
				A0C071B524F59CC800CEC1DA /* Frameworks */,
				A0C071B624F59CC800CEC1DA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A0C071BA24F59CC800CEC1DA /* PBXTargetDependency */,
			);
			name = "Tests tvOS";
			productName = SeedTruckTests;
			productReference = A0C071B824F59CC800CEC1DA /* Tests tvOS.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A045C89D24F2006400B3B1CD /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1200;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					A045C8A824F2006500B3B1CD = {
						CreatedOnToolsVersion = 12.0;
					};
					A045C8B024F2006500B3B1CD = {
						CreatedOnToolsVersion = 12.0;
					};
					A045C8B824F2006500B3B1CD = {
						CreatedOnToolsVersion = 12.0;
						TestTargetID = A045C8A824F2006500B3B1CD;
					};
					A045C8C324F2006500B3B1CD = {
						CreatedOnToolsVersion = 12.0;
						TestTargetID = A045C8B024F2006500B3B1CD;
					};
					A096385724F568CF00F2062F = {
						CreatedOnToolsVersion = 12.0;
					};
					A0C071A724F59CC700CEC1DA = {
						CreatedOnToolsVersion = 12.0;
					};
					A0C071B724F59CC800CEC1DA = {
						CreatedOnToolsVersion = 12.0;
						TestTargetID = A0C071A724F59CC700CEC1DA;
					};
				};
			};
			buildConfigurationList = A045C8A024F2006400B3B1CD /* Build configuration list for PBXProject "SeedTruck" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A045C89C24F2006400B3B1CD;
			packageReferences = (
				A022E6502C8FBC7200BAEED3 /* XCRemoteSwiftPackageReference "SwiftyBencode" */,
			);
			productRefGroup = A045C8AA24F2006500B3B1CD /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A045C8A824F2006500B3B1CD /* SeedTruck (iOS) */,
				A045C8B024F2006500B3B1CD /* SeedTruck (macOS) */,
				A045C8B824F2006500B3B1CD /* Tests iOS */,
				A045C8C324F2006500B3B1CD /* Tests macOS */,
				A096385724F568CF00F2062F /* SeedTruck (watchOS) */,
				A0C071A724F59CC700CEC1DA /* SeedTruck (tvOS) */,
				A0C071B724F59CC800CEC1DA /* Tests tvOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A045C8A724F2006500B3B1CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0D93F262E70CD670041FE7B /* AppIcon.icon in Resources */,
				A045C8CF24F2006500B3B1CD /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8AF24F2006500B3B1CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0D93F272E70CD670041FE7B /* AppIcon.icon in Resources */,
				A0F99D152569EAD6002D7BC6 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8B724F2006500B3B1CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8C224F2006500B3B1CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A096385624F568CF00F2062F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0D93F242E70CD670041FE7B /* AppIcon.icon in Resources */,
				A096387424F568D000F2062F /* Preview Assets.xcassets in Resources */,
				A096385B24F568CF00F2062F /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A0C071A624F59CC700CEC1DA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0D93F252E70CD670041FE7B /* AppIcon.icon in Resources */,
				A0C071B224F59CC800CEC1DA /* Preview Assets.xcassets in Resources */,
				A0C071AF24F59CC800CEC1DA /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A0C071B624F59CC800CEC1DA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A045C8A524F2006500B3B1CD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0BD79AF270B73C000E05093 /* RemoteServerSettingsPresenter.swift in Sources */,
				A0F99C2825620933002D7BC6 /* UTI.swift in Sources */,
				A02C585224F326EA00C82D68 /* RemoteTorrent+Transmission.swift in Sources */,
				A0AC7BB92E710A79004C65AF /* FloatingServerStatusView.swift in Sources */,
				A073DD2424F4768400BA37C9 /* TorrentDetailsPresenter.swift in Sources */,
				A011C7E524F20C8D009D72C6 /* RemoteTorrent.swift in Sources */,
				A01056A824F4901700DA056D /* LocalTorrent+Initializers.swift in Sources */,
				A0BD79AC270B71CB00E05093 /* RemoteServerSettingsView.swift in Sources */,
				A096389024F56A1200F2062F /* LocalTorrent+ComputedProperties.swift in Sources */,
				A09638A624F56BDA00F2062F /* Box.swift in Sources */,
				A0E7426525154FCB0029FFD6 /* SharedBucket.swift in Sources */,
				A0C071FF24F5AFC400CEC1DA /* ServerStatusView.swift in Sources */,
				A045C8E624F2021100B3B1CD /* TorrentItemView.swift in Sources */,
				A09638AE24F5762700F2062F /* DataTransferManager.swift in Sources */,
				A011C7F224F21088009D72C6 /* TransmissionConnection.swift in Sources */,
				A071CC6E24FBC9BF00E46065 /* Style.swift in Sources */,
				A0CE018325855834002777BF /* NewServerView+Shared.swift in Sources */,
				A01056A524F48D5E00DA056D /* TorrentHandlerView.swift in Sources */,
				A0B9B8082516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift in Sources */,
				A07DD0F42512C9D400559D9A /* TemporaryServer.swift in Sources */,
				A0B9B8442516369600E3486A /* CoreDataManagedObjectDeleter.swift in Sources */,
				A08863A52585ADD800443ECB /* Constants.swift in Sources */,
				A071CAC924F3113800B6AC0C /* ByteCountFormatter+Extensions.swift in Sources */,
				A0CE011C25853DD2002777BF /* TorrentsView+Shared.swift in Sources */,
				A0CE013725854916002777BF /* ServerDetailsView.swift in Sources */,
				A0AAF2AA257BB39600A2217A /* NoTorrentsView.swift in Sources */,
				A0589FED251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */,
				A0C1DB3924F4358500C76682 /* TorrentListView.swift in Sources */,
				A07DD1012512C9E900559D9A /* ConnectionDetails.swift in Sources */,
				A0B8C13324F3FF4E00ACD713 /* DocumentPickerAdapter.swift in Sources */,
				A0E7427A2515501F0029FFD6 /* DataTransferManageable.swift in Sources */,
				A096389A24F56A5100F2062F /* Server+CoreData.swift in Sources */,
				A07DD0BF2512C96600559D9A /* Connectable.swift in Sources */,
				A09656AC24F2C8E8009D0FE1 /* PreviewMockData.swift in Sources */,
				A0C071FA24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */,
				A05FE16324F43A5A00B57E45 /* NoServersConfiguredView.swift in Sources */,
				A011C7F524F211C6009D72C6 /* LocalTorrent.swift in Sources */,
				A0CE020625857E0C002777BF /* TorrentHandlerView+Shared.swift in Sources */,
				A05FE16624F43A7300B57E45 /* ErrorView.swift in Sources */,
				A05FE16A24F45B2700B57E45 /* LoadingView.swift in Sources */,
				A045C8E324F2013100B3B1CD /* TorrentsView.swift in Sources */,
				A0CE01422585494A002777BF /* ConnectionResult.swift in Sources */,
				A096388724F569CA00F2062F /* ServerType.swift in Sources */,
				A07FFC7024F30044002A78B5 /* Transmission+Extensions.swift in Sources */,
				A02C585924F3446100C82D68 /* NewServerView.swift in Sources */,
				A045C8E024F2011E00B3B1CD /* MainView.swift in Sources */,
				A07FFC6D24F2F34E002A78B5 /* Transmission.swift in Sources */,
				A045C8CB24F2006500B3B1CD /* SeedTruckApp.swift in Sources */,
				A09638B124F5804100F2062F /* Server+Serialization.swift in Sources */,
				A02C584F24F325A400C82D68 /* DataModel.xcdatamodeld in Sources */,
				A0B9B85F2516373300E3486A /* Box+View.swift in Sources */,
				A045C8E924F205B100B3B1CD /* ProgressBarView.swift in Sources */,
				A023BAE424F294E400B82FB3 /* TorrentDetailsView.swift in Sources */,
				A02C585624F3400200C82D68 /* Server.swift in Sources */,
				A0CE011125853D93002777BF /* MenuItem.swift in Sources */,
				A011C7EF24F21078009D72C6 /* ServerConnection.swift in Sources */,
				A023BAE724F294EF00B82FB3 /* SettingsView.swift in Sources */,
				A01056AB24F491B600DA056D /* String+Extensions.swift in Sources */,
				A0C1DB3624F42E4100C76682 /* SettingsPresenter.swift in Sources */,
				A0CE016E25854D44002777BF /* NotificationName+Extensions.swift in Sources */,
				A073DD2124F4658700BA37C9 /* AddMagnetView.swift in Sources */,
				A051D54F2E563EF800A94EDA /* LabelPickerView.swift in Sources */,
				A08863CA2585B65E00443ECB /* View+Extensions.swift in Sources */,
				A0F99C132562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8AD24F2006500B3B1CD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0CE01A725855871002777BF /* NewServerView.swift in Sources */,
				A02C585324F326EA00C82D68 /* RemoteTorrent+Transmission.swift in Sources */,
				A073DD2524F4768400BA37C9 /* TorrentDetailsPresenter.swift in Sources */,
				A024CF9D258583500068844E /* DataTransferManageable.swift in Sources */,
				A011C7E624F20C8D009D72C6 /* RemoteTorrent.swift in Sources */,
				A0C071FB24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */,
				A0CE011225853D93002777BF /* MenuItem.swift in Sources */,
				A01056A924F4901700DA056D /* LocalTorrent+Initializers.swift in Sources */,
				A06DEFA824FD7EF5004B2DC9 /* MainView.swift in Sources */,
				A0001B6F270B879400F40C2F /* RemoteServerSettingsView.swift in Sources */,
				A0CE01FC25857C9D002777BF /* TorrentHandlerView.swift in Sources */,
				A071CC6F24FBC9BF00E46065 /* Style.swift in Sources */,
				A096389124F56A1200F2062F /* LocalTorrent+ComputedProperties.swift in Sources */,
				A0BD79B0270B73C000E05093 /* RemoteServerSettingsPresenter.swift in Sources */,
				A0CE013825854916002777BF /* ServerDetailsView.swift in Sources */,
				A0CE01C1258559DD002777BF /* NewServerDoneView.swift in Sources */,
				A09638A724F56BDA00F2062F /* Box.swift in Sources */,
				A045C8E724F2021100B3B1CD /* TorrentItemView.swift in Sources */,
				A0F99D512569F676002D7BC6 /* Application.swift in Sources */,
				A011C7F324F21088009D72C6 /* TransmissionConnection.swift in Sources */,
				A0C0720024F5AFC400CEC1DA /* ServerStatusView.swift in Sources */,
				A0CE016F25854D44002777BF /* NotificationName+Extensions.swift in Sources */,
				A071CACA24F3113800B6AC0C /* ByteCountFormatter+Extensions.swift in Sources */,
				A0C1DB3A24F4358500C76682 /* TorrentListView.swift in Sources */,
				A0E7426625154FCB0029FFD6 /* SharedBucket.swift in Sources */,
				A0AAF2BB257BB44A00A2217A /* NoTorrentsView.swift in Sources */,
				A07DD1022512C9E900559D9A /* ConnectionDetails.swift in Sources */,
				A096389B24F56A5100F2062F /* Server+CoreData.swift in Sources */,
				A09656AD24F2C8E8009D0FE1 /* PreviewMockData.swift in Sources */,
				A07DD0C02512C96600559D9A /* Connectable.swift in Sources */,
				A0CE01432585494A002777BF /* ConnectionResult.swift in Sources */,
				A05FE16424F43A5A00B57E45 /* NoServersConfiguredView.swift in Sources */,
				A011C7F624F211C6009D72C6 /* LocalTorrent.swift in Sources */,
				A05FE16724F43A7300B57E45 /* ErrorView.swift in Sources */,
				A08863D32585B66900443ECB /* View+Extensions.swift in Sources */,
				A05FE16B24F45B2700B57E45 /* LoadingView.swift in Sources */,
				A0CE00F725853C53002777BF /* TorrentsView.swift in Sources */,
				A096388824F569CA00F2062F /* ServerType.swift in Sources */,
				A012561325046F0D00C7D51B /* SeedTruckApp.swift in Sources */,
				A07DD0F52512C9D400559D9A /* TemporaryServer.swift in Sources */,
				A0B9B84D2516369600E3486A /* CoreDataManagedObjectDeleter.swift in Sources */,
				A07FFC7124F30044002A78B5 /* Transmission+Extensions.swift in Sources */,
				A08864182585BB5500443ECB /* GeneralSettingsView+AutoUpdateInterval.swift in Sources */,
				A0B9B8092516367E00E3486A /* MockCoreDataManagedObjectDeleter.swift in Sources */,
				A0F99C142562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */,
				A0CE011D25853DD2002777BF /* TorrentsView+Shared.swift in Sources */,
				A08863AE2585AE1700443ECB /* Constants.swift in Sources */,
				A07FFC6E24F2F34E002A78B5 /* Transmission.swift in Sources */,
				A0CE020725857E0C002777BF /* TorrentHandlerView+Shared.swift in Sources */,
				A02C585024F325A400C82D68 /* DataModel.xcdatamodeld in Sources */,
				A0D87FF1258456260031E898 /* SettingsView.swift in Sources */,
				A051D5502E563EF800A94EDA /* LabelPickerView.swift in Sources */,
				A045C8EA24F205B100B3B1CD /* ProgressBarView.swift in Sources */,
				A0F99C2925620933002D7BC6 /* UTI.swift in Sources */,
				A023BAE524F294E400B82FB3 /* TorrentDetailsView.swift in Sources */,
				A02C585724F3400200C82D68 /* Server.swift in Sources */,
				A011C7F024F21078009D72C6 /* ServerConnection.swift in Sources */,
				A0D88003258457300031E898 /* GeneralSettingsView.swift in Sources */,
				A0F99C3425620981002D7BC6 /* TorrentFile.swift in Sources */,
				A0B9B8C32516388000E3486A /* Box+View.swift in Sources */,
				A01056AC24F491B600DA056D /* String+Extensions.swift in Sources */,
				A0589FEE251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */,
				A0C1DB3724F42E4100C76682 /* SettingsPresenter.swift in Sources */,
				A0D8800D258457370031E898 /* ServerSettingsView.swift in Sources */,
				A0CE018425855834002777BF /* NewServerView+Shared.swift in Sources */,
				A073DD2224F4658700BA37C9 /* AddMagnetView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8B524F2006500B3B1CD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A093B94A25142ACB00F499B4 /* Transmission.swift in Sources */,
				A093B95325142B2400F499B4 /* Transmission+Extensions.swift in Sources */,
				A045C8BE24F2006500B3B1CD /* Tests_iOS.swift in Sources */,
				A07FFC7324F306AC002A78B5 /* TransmissionModelTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A045C8C024F2006500B3B1CD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A045C8C924F2006500B3B1CD /* Tests_macOS.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A096385D24F568CF00F2062F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A06DEFA424FC8219004B2DC9 /* NoServersConfiguredView.swift in Sources */,
				A09638B724F58BD300F2062F /* ServerView.swift in Sources */,
				A096389F24F56A8A00F2062F /* RemoteTorrent+Transmission.swift in Sources */,
				A09638AC24F5746600F2062F /* DataTransferManager.swift in Sources */,
				A096388924F569CA00F2062F /* ServerType.swift in Sources */,
				A0589FEF251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */,
				A09638A424F56BAE00F2062F /* ByteCountFormatter+Extensions.swift in Sources */,
				A09638A124F56A9C00F2062F /* TransmissionConnection.swift in Sources */,
				A0E7427C2515501F0029FFD6 /* DataTransferManageable.swift in Sources */,
				A096388424F5699900F2062F /* RemoteTorrent.swift in Sources */,
				A08863DC2585B66A00443ECB /* View+Extensions.swift in Sources */,
				A096389424F56A2500F2062F /* TorrentDetailsPresenter.swift in Sources */,
				A096386924F568CF00F2062F /* MainView.swift in Sources */,
				A0AAF2C4257BB44B00A2217A /* NoTorrentsView.swift in Sources */,
				A07DD0F62512C9D400559D9A /* TemporaryServer.swift in Sources */,
				A07DD0C12512C96600559D9A /* Connectable.swift in Sources */,
				A08863B72585AE1900443ECB /* Constants.swift in Sources */,
				A0CE01CA25855BA1002777BF /* NotificationName+Extensions.swift in Sources */,
				A096388B24F569DE00F2062F /* LoadingView.swift in Sources */,
				A096388C24F569DE00F2062F /* ErrorView.swift in Sources */,
				A07DD1032512C9E900559D9A /* ConnectionDetails.swift in Sources */,
				A09638AF24F5802800F2062F /* Server+CoreData.swift in Sources */,
				A096389E24F56A8200F2062F /* Transmission.swift in Sources */,
				A0F99C152562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */,
				A0C071FC24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */,
				A09638A024F56A9300F2062F /* ServerConnection.swift in Sources */,
				A09638A324F56B9F00F2062F /* ProgressBarView.swift in Sources */,
				A096389D24F56A8200F2062F /* Transmission+Extensions.swift in Sources */,
				A096386724F568CF00F2062F /* SeedTruckApp.swift in Sources */,
				A096388224F5697600F2062F /* TorrentDetailsView.swift in Sources */,
				A071CC7024FBC9BF00E46065 /* Style.swift in Sources */,
				A096388D24F569E900F2062F /* PreviewMockData.swift in Sources */,
				A0B9B88B251637D500E3486A /* Box+View.swift in Sources */,
				A09638A824F56BDA00F2062F /* Box.swift in Sources */,
				A096388524F569A900F2062F /* Server.swift in Sources */,
				A09638AA24F5718C00F2062F /* DataModel.xcdatamodeld in Sources */,
				A0E7426725154FCB0029FFD6 /* SharedBucket.swift in Sources */,
				A09638B324F5804100F2062F /* Server+Serialization.swift in Sources */,
				A0C0720424F5B03700CEC1DA /* ServerStatusView.swift in Sources */,
				A096388324F5697600F2062F /* TorrentListView.swift in Sources */,
				A09638A224F56B9700F2062F /* TorrentItemView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A0C071A424F59CC700CEC1DA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0C0720624F5B90300CEC1DA /* DataModel.xcdatamodeld in Sources */,
				A0F99C162562028C002D7BC6 /* NSPersistentContainer+Extensions.swift in Sources */,
				A0C071F124F5A06100CEC1DA /* NoServersConfiguredView.swift in Sources */,
				A0C071DD24F5A05300CEC1DA /* RemoteTorrent+Transmission.swift in Sources */,
				A0C071E624F5A05900CEC1DA /* TransmissionConnection.swift in Sources */,
				A0C071DA24F5A05300CEC1DA /* Transmission+Extensions.swift in Sources */,
				A0C071F624F5A06100CEC1DA /* LoadingView.swift in Sources */,
				A0C071E924F5A05900CEC1DA /* String+Extensions.swift in Sources */,
				A08863E52585B66B00443ECB /* View+Extensions.swift in Sources */,
				A0C071D624F5A03500CEC1DA /* TorrentDetailsPresenter.swift in Sources */,
				A0C0720524F5B04500CEC1DA /* ServerStatusView.swift in Sources */,
				A0E7426825154FCB0029FFD6 /* SharedBucket.swift in Sources */,
				A0C071F324F5A06100CEC1DA /* TorrentDetailsView.swift in Sources */,
				A0C071EE24F5A06100CEC1DA /* ErrorView.swift in Sources */,
				A0C071E224F5A05900CEC1DA /* ByteCountFormatter+Extensions.swift in Sources */,
				A0C071DC24F5A05300CEC1DA /* Transmission.swift in Sources */,
				A0C071F024F5A06100CEC1DA /* Box.swift in Sources */,
				A0C071F824F5A17C00CEC1DA /* SeedTruckApp.swift in Sources */,
				A0AAF2C5257BB44C00A2217A /* NoTorrentsView.swift in Sources */,
				A0C071E024F5A05300CEC1DA /* ServerType.swift in Sources */,
				A0C071D524F5A03500CEC1DA /* ProgressBarView.swift in Sources */,
				A0C071AD24F59CC700CEC1DA /* MainView.swift in Sources */,
				A0CE017025854D44002777BF /* NotificationName+Extensions.swift in Sources */,
				A0C071FD24F5AC7000CEC1DA /* RemoteTorrent+Convenience.swift in Sources */,
				A07DD1042512C9E900559D9A /* ConnectionDetails.swift in Sources */,
				A0CE018525855834002777BF /* NewServerView+Shared.swift in Sources */,
				A0C071DF24F5A05300CEC1DA /* RemoteTorrent.swift in Sources */,
				A0C071F424F5A06100CEC1DA /* TorrentItemView.swift in Sources */,
				A07DD0C22512C96600559D9A /* Connectable.swift in Sources */,
				A0C071D924F5A04700CEC1DA /* Server+CoreData.swift in Sources */,
				A0C071F724F5A15D00CEC1DA /* TorrentListView.swift in Sources */,
				A0E7427D2515501F0029FFD6 /* DataTransferManageable.swift in Sources */,
				A0C071D724F5A04700CEC1DA /* Server.swift in Sources */,
				A07DD0F72512C9D400559D9A /* TemporaryServer.swift in Sources */,
				A0589FF0251799F30022D839 /* BinaryInteger+Extensions.swift in Sources */,
				A0C071EF24F5A06100CEC1DA /* SettingsView.swift in Sources */,
				A08863C02585AE1900443ECB /* Constants.swift in Sources */,
				A0C071EA24F5A06100CEC1DA /* NewServerView.swift in Sources */,
				A0C071D424F5A03500CEC1DA /* SettingsPresenter.swift in Sources */,
				A0B9B89E2516380C00E3486A /* Box+View.swift in Sources */,
				A0C071E324F5A05900CEC1DA /* ServerConnection.swift in Sources */,
				A071CC7124FBC9BF00E46065 /* Style.swift in Sources */,
				A0CE01442585494A002777BF /* ConnectionResult.swift in Sources */,
				A0B9B8B82516385F00E3486A /* MockCoreDataManagedObjectDeleter.swift in Sources */,
				A0B9B8AF2516384300E3486A /* CoreDataManagedObjectDeleter.swift in Sources */,
				A0C071E824F5A05900CEC1DA /* PreviewMockData.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A0C071B424F59CC800CEC1DA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A0C071BD24F59CC800CEC1DA /* SeedTruckTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A045C8BB24F2006500B3B1CD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A045C8A824F2006500B3B1CD /* SeedTruck (iOS) */;
			targetProxy = A045C8BA24F2006500B3B1CD /* PBXContainerItemProxy */;
		};
		A045C8C624F2006500B3B1CD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A045C8B024F2006500B3B1CD /* SeedTruck (macOS) */;
			targetProxy = A045C8C524F2006500B3B1CD /* PBXContainerItemProxy */;
		};
		A096387824F568D000F2062F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A096385724F568CF00F2062F /* SeedTruck (watchOS) */;
			targetProxy = A096387724F568D000F2062F /* PBXContainerItemProxy */;
		};
		A0C071BA24F59CC800CEC1DA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A0C071A724F59CC700CEC1DA /* SeedTruck (tvOS) */;
			targetProxy = A0C071B924F59CC800CEC1DA /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A045C8D124F2006500B3B1CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 3D7R27DBRZ;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A045C8D224F2006500B3B1CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 3D7R27DBRZ;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		A045C8D424F2006500B3B1CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 18;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = iOS/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.1;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.seedtruck;
				PRODUCT_NAME = SeedTruck;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A045C8D524F2006500B3B1CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 18;
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = iOS/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3.1;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.seedtruck;
				PRODUCT_NAME = SeedTruck;
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A045C8D724F2006500B3B1CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = macOS/macOS.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 18;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_OUTGOING_NETWORK_CONNECTIONS = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				INFOPLIST_FILE = macOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.3.1;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.seedtruck;
				PRODUCT_NAME = SeedTruck;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = macosx;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		A045C8D824F2006500B3B1CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = macOS/macOS.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 18;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_OUTGOING_NETWORK_CONNECTIONS = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				INFOPLIST_FILE = macOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.3.1;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.seedtruck;
				PRODUCT_NAME = SeedTruck;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = macosx;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		A045C8DA24F2006500B3B1CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = "Tests iOS/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "io.edr.Tests-iOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "SeedTruck (iOS)";
			};
			name = Debug;
		};
		A045C8DB24F2006500B3B1CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = "Tests iOS/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "io.edr.Tests-iOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "SeedTruck (iOS)";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A045C8DD24F2006500B3B1CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				INFOPLIST_FILE = "Tests macOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_BUNDLE_IDENTIFIER = "io.edr.Tests-macOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = "SeedTruck (macOS)";
			};
			name = Debug;
		};
		A045C8DE24F2006500B3B1CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				INFOPLIST_FILE = "Tests macOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				PRODUCT_BUNDLE_IDENTIFIER = "io.edr.Tests-macOS";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = "SeedTruck (macOS)";
			};
			name = Release;
		};
		A096387B24F568D000F2062F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 18;
				INFOPLIST_FILE = watchOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.3.1;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.seedtruck.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 8.0;
			};
			name = Debug;
		};
		A096387C24F568D000F2062F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 18;
				INFOPLIST_FILE = watchOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.3.1;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.seedtruck.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 8.0;
			};
			name = Release;
		};
		A0C071CA24F59CC800CEC1DA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 17;
				DEVELOPMENT_ASSET_PATHS = "\"tvOS/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = tvOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.SeedTruck;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 14.0;
			};
			name = Debug;
		};
		A0C071CB24F59CC800CEC1DA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 17;
				DEVELOPMENT_ASSET_PATHS = "\"tvOS/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = tvOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.SeedTruck;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 14.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A0C071CC24F59CC800CEC1DA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = "Tests tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.SeedTruckTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SeedTruck.app/SeedTruck";
				TVOS_DEPLOYMENT_TARGET = 14.0;
			};
			name = Debug;
		};
		A0C071CD24F59CC800CEC1DA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = "Tests tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = io.edr.SeedTruckTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SeedTruck.app/SeedTruck";
				TVOS_DEPLOYMENT_TARGET = 14.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A045C8A024F2006400B3B1CD /* Build configuration list for PBXProject "SeedTruck" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A045C8D124F2006500B3B1CD /* Debug */,
				A045C8D224F2006500B3B1CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A045C8D324F2006500B3B1CD /* Build configuration list for PBXNativeTarget "SeedTruck (iOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A045C8D424F2006500B3B1CD /* Debug */,
				A045C8D524F2006500B3B1CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A045C8D624F2006500B3B1CD /* Build configuration list for PBXNativeTarget "SeedTruck (macOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A045C8D724F2006500B3B1CD /* Debug */,
				A045C8D824F2006500B3B1CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A045C8D924F2006500B3B1CD /* Build configuration list for PBXNativeTarget "Tests iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A045C8DA24F2006500B3B1CD /* Debug */,
				A045C8DB24F2006500B3B1CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A045C8DC24F2006500B3B1CD /* Build configuration list for PBXNativeTarget "Tests macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A045C8DD24F2006500B3B1CD /* Debug */,
				A045C8DE24F2006500B3B1CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A096388124F568D000F2062F /* Build configuration list for PBXNativeTarget "SeedTruck (watchOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A096387B24F568D000F2062F /* Debug */,
				A096387C24F568D000F2062F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A0C071D024F59CC800CEC1DA /* Build configuration list for PBXNativeTarget "SeedTruck (tvOS)" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A0C071CA24F59CC800CEC1DA /* Debug */,
				A0C071CB24F59CC800CEC1DA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A0C071D124F59CC800CEC1DA /* Build configuration list for PBXNativeTarget "Tests tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A0C071CC24F59CC800CEC1DA /* Debug */,
				A0C071CD24F59CC800CEC1DA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		A022E6502C8FBC7200BAEED3 /* XCRemoteSwiftPackageReference "SwiftyBencode" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/edualm/SwiftyBencode";
			requirement = {
				branch = develop;
				kind = branch;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		A022E6512C8FBC7200BAEED3 /* SwiftyBencode */ = {
			isa = XCSwiftPackageProductDependency;
			package = A022E6502C8FBC7200BAEED3 /* XCRemoteSwiftPackageReference "SwiftyBencode" */;
			productName = SwiftyBencode;
		};
		A022E6532C8FBC8400BAEED3 /* SwiftyBencode */ = {
			isa = XCSwiftPackageProductDependency;
			package = A022E6502C8FBC7200BAEED3 /* XCRemoteSwiftPackageReference "SwiftyBencode" */;
			productName = SwiftyBencode;
		};
		A022E6552C8FBC9000BAEED3 /* SwiftyBencode */ = {
			isa = XCSwiftPackageProductDependency;
			package = A022E6502C8FBC7200BAEED3 /* XCRemoteSwiftPackageReference "SwiftyBencode" */;
			productName = SwiftyBencode;
		};
		A022E6572C8FBCCE00BAEED3 /* SwiftyBencode */ = {
			isa = XCSwiftPackageProductDependency;
			package = A022E6502C8FBC7200BAEED3 /* XCRemoteSwiftPackageReference "SwiftyBencode" */;
			productName = SwiftyBencode;
		};
/* End XCSwiftPackageProductDependency section */

/* Begin XCVersionGroup section */
		A02C584D24F325A400C82D68 /* DataModel.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				A02C584E24F325A400C82D68 /* DataModel.xcdatamodel */,
			);
			currentVersion = A02C584E24F325A400C82D68 /* DataModel.xcdatamodel */;
			path = DataModel.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = A045C89D24F2006400B3B1CD /* Project object */;
}
