//
//  String+Extensions.swift
//  SeedTruck
//
//  Created by <PERSON> on 25/08/2020.
//

import Foundation

extension String {
    
    //  https://stackoverflow.com/a/31727051

    func slice(from: String, to: String) -> String? {
        return (range(of: from)?.upperBound).flatMap { substringFrom in
            (range(of: to, range: substringFrom..<endIndex)?.lowerBound).map { substringTo in
                String(self[substringFrom..<substringTo])
            }
        }
    }
}
