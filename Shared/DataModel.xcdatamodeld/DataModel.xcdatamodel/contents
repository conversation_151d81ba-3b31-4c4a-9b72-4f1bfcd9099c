<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="17189" systemVersion="19G73" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Server" representedClassName=".Server" syncable="YES">
        <attribute name="credentialPassword" optional="YES" attributeType="String"/>
        <attribute name="credentialUsername" optional="YES" attributeType="String"/>
        <attribute name="endpoint" attributeType="URI"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="type" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <uniquenessConstraints>
            <uniquenessConstraint>
                <constraint value="name"/>
            </uniquenessConstraint>
        </uniquenessConstraints>
    </entity>
    <elements>
        <element name="Server" positionX="-63" positionY="-18" width="128" height="118"/>
    </elements>
</model>