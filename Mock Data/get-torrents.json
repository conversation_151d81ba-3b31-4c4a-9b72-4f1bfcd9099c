{"arguments": {"torrents": [{"addedDate": 0, "downloadDir": "/downloads", "error": 0, "errorString": "", "eta": -1, "id": 1, "isFinished": false, "isStalled": true, "leftUntilDone": 0, "metadataPercentComplete": 1, "name": "<PERSON>", "peersConnected": 0, "peersGettingFromUs": 0, "peersSendingToUs": 0, "percentDone": 1, "queuePosition": 0, "rateDownload": 0, "rateUpload": 100000, "recheckProgress": 0, "seedRatioLimit": 2, "seedRatioMode": 0, "sizeWhenDone": 1000000, "status": 6, "totalSize": 1000000, "uploadRatio": 2.5, "uploadedEver": 1000000, "webseedsSendingToUs": 0}, {"addedDate": 0, "downloadDir": "/downloads", "error": 0, "errorString": "", "eta": 1000, "id": 2, "isFinished": false, "isStalled": true, "leftUntilDone": 0, "metadataPercentComplete": 1, "name": "Cosmos Laundromat", "peersConnected": 0, "peersGettingFromUs": 0, "peersSendingToUs": 0, "percentDone": 0.25, "queuePosition": 0, "rateDownload": 10000000, "rateUpload": 1000000, "recheckProgress": 0, "seedRatioLimit": 2, "seedRatioMode": 0, "sizeWhenDone": 1000000, "status": 4, "totalSize": 500000, "uploadRatio": 0.01, "uploadedEver": 1000000000, "webseedsSendingToUs": 0}, {"addedDate": 0, "downloadDir": "/downloads", "error": 0, "errorString": "", "eta": 1000, "id": 3, "isFinished": false, "isStalled": true, "leftUntilDone": 0, "metadataPercentComplete": 1, "name": "Tears of Steel", "peersConnected": 0, "peersGettingFromUs": 0, "peersSendingToUs": 0, "percentDone": 0, "queuePosition": 0, "rateDownload": 10000000, "rateUpload": 1000000, "recheckProgress": 0, "seedRatioLimit": 2, "seedRatioMode": 0, "sizeWhenDone": 1000000, "status": 0, "totalSize": 500000, "uploadRatio": 0.01, "uploadedEver": 1000000000, "webseedsSendingToUs": 0}]}, "result": "success"}